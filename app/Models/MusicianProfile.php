<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MusicianProfile extends Model
{
    protected $appends = ['header_image_url'];

    protected $fillable = [
        'user_id',
        'country_code',
        'phone_number',
        'website',
        'description',
        'header_image',
        'roles',
        'offered_services',
        'instruments',
        'music_types',
        'spoken_languages',
        'payment_methods',
        'rate_per_hour',
        'rate_per_event',
        'social_links',
        'tags',
        'location',
        'average_rating',
        'ratings_count'
    ];

    protected $casts = [
        'roles' => 'array',
        'offered_services' => 'array',
        'instruments' => 'array',
        'music_types' => 'array',
        'spoken_languages' => 'array',
        'payment_methods' => 'array',
        'tags' => 'array',
        'social_links' => 'array',
    ];

    public function getHeaderImageUrlAttribute()
    {
        return $this->header_image ? url('storage/banner_images/' . $this->header_image) : asset('images/banner.jpeg');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function ratings()
    {
        return $this->hasMany(Rating::class);
    }

    public function galleryImages()
    {
        return $this->hasMany(MusicianGallery::class);
    }

    public function isProfileCompleted(): bool
    {
        $requiredFields = [
            'country_code',
            'phone_number',
            'description',
            'roles',
            'offered_services',
            'instruments',
            'music_types',
            'spoken_languages',
            'payment_methods',
            'rate_per_hour',
            'rate_per_event',
            'social_links',
            'tags',
            'location'
        ];

        foreach ($requiredFields as $field) {
            $value = $this->{$field};

            // Consider empty array or null as unfilled
            if (is_array($value) && empty($value)) {
                return false;
            }

            if (is_null($value) || (is_string($value) && trim($value) === '')) {
                return false;
            }
        }

        return true;
    }
}
