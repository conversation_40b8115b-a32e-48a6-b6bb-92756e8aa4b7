<?php

namespace App\Events;

use App\Models\Message;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UnreadCountUpdated implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $userId;
    public $unreadCount;

    public function __construct($userId)
    {
        $this->userId = $userId;

        // Get receiver's total unread count efficiently
        $receiverUnreadCount = Message::whereHas('chat', function ($query) use ($userId) {
            $query->where(function ($q) use ($userId) {
                $q->where('client_id', $userId)
                    ->orWhere('musician_id', $userId);
            });
        })
            ->where('is_read', false)
            ->where('sender_id', '!=', $userId)
            ->count();

        $this->unreadCount = $receiverUnreadCount;
    }

    public function broadcastOn(): array
    {
        return [new Channel('user.' . $this->userId)];
    }

    public function broadcastWith(): array
    {
        return [
            'unread_count' => $this->unreadCount
        ];
    }
}
