<?php

namespace App\Events;

use App\Models\Notification;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UnreadNotificationsCountUpdated implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;


    public $userId;
    public $unreadCount;

    public function __construct($userId)
    {
        $this->userId = $userId;

        $this->unreadCount = Notification::where('user_id', $userId)
            ->where('is_read', 0)
            ->count();
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */

    public function broadcastOn(): array
    {
        return [new Channel('user.' . $this->userId)];
    }

    public function broadcastWith(): array
    {
        return [
            'unread_count' => $this->unreadCount
        ];
    }
}
