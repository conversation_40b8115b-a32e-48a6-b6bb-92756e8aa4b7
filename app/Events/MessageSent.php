<?php

namespace App\Events;

use App\Models\Message;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MessageSent implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;
    public $message;
    public $receiverId;
    public $isNewChat;

    public function __construct(Message $message, $receiverId, $isNewChat)
    {
        $this->message = $message->load([
            'sender:id,name,image',
        ]);
        $this->receiverId = $receiverId;
        $this->isNewChat = $isNewChat;
    }

    public function broadcastOn(): array
    {
        return [new Channel('chat.' . $this->receiverId)];
    }

    public function broadcastWith(): array
    {
        return [
            'id' => $this->message->id,
            'chat_id' => $this->message->chat_id,
            'sender_id' => $this->message->sender_id,
            'sender' => $this->message->sender,
            'message' => $this->message->message,
            'is_read' => $this->message->is_read,
            'created_at' => $this->message->created_at,
            'formatted_created_at' => $this->message->formatted_created_at,
            'is_new_chat' => $this->isNewChat
        ];
    }
}
