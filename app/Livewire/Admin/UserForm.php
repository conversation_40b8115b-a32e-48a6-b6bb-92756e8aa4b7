<?php

namespace App\Livewire\Admin;

use App\Models\User;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class UserForm extends Component
{
    use WithFileUploads;

    // User ID for edit mode
    public $userId = null;

    // Basic user fields
    public $name = '';
    public $email = '';
    public $password = '';
    public $password_confirmation = '';
    public $role = 'client';
    public $image = null;
    public $old_image = null;
    public $is_active = true;
    public $is_approved = false;
    public $is_verified = true;
    public $is_vip_user = false;

    // Musician profile fields
    public $phone_number = '';
    public $website = '';
    public $description = '';
    public $header_image = null;
    public $old_header_image = null;
    public $roles = [];
    public $offered_services = [];
    public $instruments = [];
    public $music_types = [];
    public $spoken_languages = [];
    public $payment_methods = [];
    public $rate_per_hour = 0;
    public $rate_per_event = 0;
    public $social_links = [];
    public $tags = '';
    public $location = '';

    // Available social networks
    public $availableSocialNetworks = ['Instagram', 'Facebook', 'YouTube', 'TikTok', 'Twitter', "LinkedIn", 'SoundCloud', 'Spotify'];
    public $selectedSocialNetworks = [];

    // UI state
    public $activeTab = 'basic';

    // Options for multi-select fields
    public $availableRoles = ['Musician', 'DJ', 'Manager', 'Tech Support'];
    public $availableServices = ['Performing', 'Tutoring', 'Booking Management', 'Tech Support', 'Gig Replacement'];

    public $availableInstruments = [
        'Guitar',
        'Piano',
        'Drums',
        'Bass',
        'Violin',
        'Saxophone',
        'Trumpet',
        'Flute',
        'Clarinet',
        'Cello',
        'Harp',
        'Banjo',
        'Accordion',
        'Vocals'
    ];
    public $availableMusicTypes = [
        'Pop',
        'Classical',
        'Country',
        'Folk',
        'R&B',
        'Reggae',
        'Indie',
        'Rock / Heavy metal',
        'Jazz / Blues',
        'Hip-Hop / Rap',
        'Electronic (EDM)',
        'Latin (Mambo, Salsa, Cha Cha)',
        'Afrobeat / African rhythms',
        'Ballroom (Waltz, Foxtrot, Tango, Crooners)',
        'Cabaret / Broadway',
        'Celtic / Christian / Spiritual',
        'Chill / Relaxing beats',
        'Drum & Bass / Dubstep',
        'Flamenco / Spanish guitar',
        'Gospel / Gregorian',
        'House / Trance',
        'Post-Rock & New Age',
        'Punk Rock / Thrash / Grunge'
    ];
    public $availableLanguages = [
        'English',
        'Spanish',
        'French',
        'German',
        'Italian',
        'Portuguese',
        'Russian',
        'Japanese',
        'Chinese',
        'Arabic',
        'Hindi'
    ];
    public $availablePaymentMethods = ['Credit Cards', 'PayPal', 'Crypto', 'Bank transfer'];

    public function mount($userId = null)
    {
        $this->userId = $userId;

        if ($userId) {
            $this->loadUser($userId);
        }
    }

    public function loadUser($userId)
    {
        $user = User::with('musicianProfile')->findOrFail($userId);

        // Load user data
        $this->name = $user->name;
        $this->email = $user->email;
        $this->role = $user->role;
        $this->old_image = $user->image_url;
        $this->is_active = $user->is_active ? true : false;
        $this->is_approved = $user->is_approved ? true : false;
        $this->is_verified = $user->is_verified ? true : false;
        $this->is_vip_user = $user->is_vip_user ? true : false;

        // Load musician profile data if exists
        if ($user->role === 'musician' && $user->musicianProfile) {
            $profile = $user->musicianProfile;

            $this->phone_number = $profile->phone_number ?? '';
            $this->website = $profile->website ?? '';
            $this->description = $profile->description ?? '';
            $this->old_header_image = $profile->header_image_url;
            $this->roles = $profile->roles ?? [];
            $this->offered_services = $profile->offered_services ?? [];
            $this->instruments = $profile->instruments ?? [];
            $this->music_types = $profile->music_types ?? [];
            $this->spoken_languages = $profile->spoken_languages ?? [];
            $this->payment_methods = $profile->payment_methods ?? [];
            $this->rate_per_hour = $profile->rate_per_hour ?? 0;
            $this->rate_per_event = $profile->rate_per_event ?? 0;

            // Load social links and convert from old format if needed
            $socialLinksData = $profile->social_links ?? [];
            $this->social_links = $this->convertSocialLinksToNewFormat($socialLinksData);

            // Set selected social networks based on social_links array
            $this->selectedSocialNetworks = [];
            if (!empty($this->social_links)) {
                foreach ($this->social_links as $socialLink) {
                    if (!empty($socialLink['key']) && !empty($socialLink['value'])) {
                        $this->selectedSocialNetworks[] = ucfirst($socialLink['key']);
                    }
                }
            }

            $this->tags = implode(',', $profile->tags ?? []);
            $this->location = $profile->location ?? '';
        }
    }

    public function setTab($tab)
    {
        $this->activeTab = $tab;
    }

    public function updatedSelectedSocialNetworks($propertyName)
    {
        $this->updateSocialLinks();
    }

    protected function convertSocialLinksToNewFormat($socialLinksData)
    {
        // If already in new format (array of objects), return as is
        if (is_array($socialLinksData) && !empty($socialLinksData) && isset($socialLinksData[0]['key'])) {
            return $socialLinksData;
        }

        // Convert from old format (object) to new format (array of key-value pairs)
        $newFormat = [];
        if (is_array($socialLinksData)) {
            foreach ($socialLinksData as $key => $value) {
                if (!empty($value)) {
                    $newFormat[] = [
                        'key' => $key,
                        'value' => $value
                    ];
                }
            }
        }

        return $newFormat;
    }

    protected function updateSocialLinks()
    {
        $newSocialLinks = [];

        // Keep existing values for selected networks
        foreach ($this->selectedSocialNetworks as $network) {
            $key = strtolower($network);

            // Find existing value in current social_links array
            $existingValue = '';
            foreach ($this->social_links as $socialLink) {
                if (isset($socialLink['key']) && $socialLink['key'] === $key) {
                    $existingValue = $socialLink['value'] ?? '';
                    break;
                }
            }

            $newSocialLinks[] = [
                'key' => $key,
                'value' => $existingValue
            ];
        }

        $this->social_links = $newSocialLinks;
    }

    public function updateSocialLinkValue($network, $value)
    {
        $key = strtolower($network);

        // Find and update the social link in the array
        foreach ($this->social_links as $index => $socialLink) {
            if (isset($socialLink['key']) && $socialLink['key'] === $key) {
                $this->social_links[$index]['value'] = $value;
                return;
            }
        }

        // If not found, add new entry
        $this->social_links[] = [
            'key' => $key,
            'value' => $value
        ];
    }

    // Convert comma-separated string to array for tags
    protected function prepareTags()
    {
        if (is_string($this->tags)) {
            return array_map('trim', explode(',', $this->tags));
        }

        return $this->tags;
    }

    // Prepare social links for saving - filter out empty values
    protected function prepareSocialLinksForSaving()
    {
        $filteredLinks = [];

        foreach ($this->social_links as $socialLink) {
            if (!empty($socialLink['key']) && !empty($socialLink['value'])) {
                $filteredLinks[] = [
                    'key' => $socialLink['key'],
                    'value' => $socialLink['value']
                ];
            }
        }

        return $filteredLinks;
    }

    public function save()
    {
        $this->validate($this->getValidationRules());

        if ($this->userId) {
            $this->updateUser();
        } else {
            $this->createUser();
        }

        return redirect()->route('users-management')->with(
            'success',
            $this->userId ? 'User updated successfully' : 'User created successfully'
        );
    }

    protected function getValidationRules()
    {
        $rules = [
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'email',
                'max:255',
                Rule::unique('users')->ignore($this->userId)
            ],
            'is_active' => 'boolean',
            'is_approved' => 'boolean',
            'is_verified' => 'boolean',
            'is_vip_user' => 'boolean',
            'role' => 'required|in:musician,client',
            'image' => 'nullable|image|max:10240',
        ];

        // Add password validation for new users
        if (!$this->userId) {
            $rules['password'] = 'required|min:8|confirmed';
        } else if ($this->password) {
            $rules['password'] = 'min:8|confirmed';
        }

        // Add musician profile validation if role is musician
        if ($this->role === 'musician') {
            $rules = array_merge($rules, [
                'phone_number' => 'nullable|string|max:20',
                'website' => 'nullable|url|max:255',
                'description' => 'nullable|string|max:2000',
                'header_image' => 'nullable|image|max:10240',
                'roles' => 'nullable|array',
                'offered_services' => 'nullable|array',
                'instruments' => 'nullable|array',
                'music_types' => 'nullable|array',
                'spoken_languages' => 'nullable|array',
                'payment_methods' => 'nullable|array',
                'rate_per_hour' => 'nullable|numeric|min:0',
                'rate_per_event' => 'nullable|numeric|min:0',
                'social_links' => 'nullable|array',
                'social_links.*.key' => 'nullable|string|max:50',
                'social_links.*.value' => 'nullable|string|max:255',
                'selectedSocialNetworks' => 'nullable|array',
                'tags' => 'nullable',
                'location' => 'nullable|string|max:255',
            ]);
        }

        return $rules;
    }

    protected function createUser()
    {
        // Create user
        $userData = [
            'name' => $this->name,
            'email' => $this->email,
            'password' => Hash::make($this->password),
            'role' => $this->role,
            'is_active' => $this->is_active,
            'is_approved' => $this->is_approved,
            'is_verified' => $this->is_verified,
            'is_vip_user' => $this->is_vip_user,
        ];

        // Handle profile image upload
        if ($this->image) {
            $userData['image'] = uploadFile($this->image, 'profile_photos');
        }

        $user = User::create($userData);

        // Create musician profile if role is musician
        if ($this->role === 'musician') {
            $this->createOrUpdateMusicianProfile($user);
        }

        $this->dispatch(
            'notify',
            message: 'User created successfully',
            type: 'success',
        );
    }

    protected function updateUser()
    {
        $user = User::findOrFail($this->userId);

        // Update user data
        $userData = [
            'name' => $this->name,
            'email' => $this->email,
            'role' => $this->role,
            'is_active' => $this->is_active,
            'is_approved' => $this->is_approved,
            'is_verified' => $this->is_verified,
            'is_vip_user' => $this->is_vip_user,
        ];

        // Update password if provided
        if ($this->password) {
            $userData['password'] = Hash::make($this->password);
        }

        // Handle profile image upload
        if ($this->image) {
            $userData['image'] = uploadFile($this->image, 'profile_photos', $user->image);
        }

        $user->update($userData);

        // Handle musician profile
        if ($this->role === 'musician') {
            $this->createOrUpdateMusicianProfile($user);
        } else if ($user->musicianProfile) {
            // If role changed from musician to client, we might want to keep the profile
            // but we could also delete it if that's the desired behavior
            // $user->musicianProfile->delete();
        }

        $this->dispatch(
            'notify',
            message: 'User updated successfully',
            type: 'success',
        );
    }

    protected function createOrUpdateMusicianProfile($user)
    {
        $profileData = [
            'phone_number' => $this->phone_number,
            'website' => $this->website,
            'description' => $this->description,
            'roles' => $this->roles,
            'offered_services' => $this->offered_services,
            'instruments' => $this->instruments,
            'music_types' => $this->music_types,
            'spoken_languages' => $this->spoken_languages,
            'payment_methods' => $this->payment_methods,
            'rate_per_hour' => $this->rate_per_hour,
            'rate_per_event' => $this->rate_per_event,
            'social_links' => $this->prepareSocialLinksForSaving(),
            'tags' => $this->prepareTags(),
            'location' => $this->location,
        ];


        // Handle header image upload
        if ($this->header_image) {
            $oldHeaderImage = $user->musicianProfile->header_image ?? null;
            $profileData['header_image'] = uploadFile(
                $this->header_image,
                'musician_header_images',
                $oldHeaderImage
            );
        }

        // Create or update musician profile
        if ($user->musicianProfile) {
            $user->musicianProfile->update($profileData);
        } else {
            $user->musicianProfile()->create($profileData);
        }

        if ($user->musicianProfile?->isProfileCompleted()) {
            $user->is_profile_completed = true;
            $user->save();
        }
    }

    public function render()
    {
        return view('livewire.admin.user-form');
    }
}
