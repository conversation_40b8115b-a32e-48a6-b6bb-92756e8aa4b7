<?php

namespace App\Http\Controllers\Mobile;

use App\Http\Controllers\Controller;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use App\Models\Transaction;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Laravel\Sanctum\PersonalAccessToken;
use Stripe\Exception\CardException;
use Stripe\Stripe;
use Stripe\StripeClient;

class SubscriptionController extends Controller
{
    protected $stripe;

    public function __construct()
    {
        // Initialize Stripe with API key from config
        Stripe::setApiKey(env('STRIPE_SECRET'));
        $this->stripe = new StripeClient(env('STRIPE_SECRET'));
    }

    /**
     * Get user from token
     */
    protected function getUserFromToken($token)
    {
        $token = urldecode($token);
        $accessToken = PersonalAccessToken::findToken($token);

        if (!$accessToken) {
            abort(401, 'Invalid token');
        }

        return $accessToken->tokenable;
    }

    /**
     * Show subscription form
     */
    public function showSubscriptionForm($token, $planId)
    {
        try {
            $user = $this->getUserFromToken($token);
            $plan = SubscriptionPlan::findOrFail($planId);

            // Check if user already has an active subscription
            if ($user->hasActiveSubscription()) {
                return view('mobile.subscription.error', [
                    'message' => 'You already have an active subscription'
                ]);
            }

            // Check if plan is appropriate for user role
            if ($plan->user_type !== $user->role) {
                return view('mobile.subscription.error', [
                    'message' => 'This subscription plan is not available for your account type'
                ]);
            }

            return view('mobile.subscription.form', [
                'user' => $user,
                'plan' => $plan,
                'stripeKey' => env('STRIPE_KEY')
            ]);
        } catch (Exception $e) {
            Log::error('Error showing subscription form: ' . $e->getMessage());
            return view('mobile.subscription.error', [
                'message' => 'Failed to load subscription form: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Process subscription
     */
    public function processSubscription(Request $request, $token, $planId)
    {
        try {
            $user = $this->getUserFromToken($token);
            $plan = SubscriptionPlan::findOrFail($planId);

            // Validate request
            $validator = Validator::make($request->all(), [
                'payment_method_id' => 'required|string',
            ]);

            if ($validator->fails()) {
                return view('mobile.subscription.error', [
                    'message' => 'Invalid payment information: ' . $validator->errors()->first()
                ]);
            }

            // Check if user already has an active subscription
            if ($user->hasActiveSubscription()) {
                return view('mobile.subscription.error', [
                    'message' => 'You already have an active subscription'
                ]);
            }

            // Check if plan is appropriate for user role
            if ($plan->user_type !== $user->role) {
                return view('mobile.subscription.error', [
                    'message' => 'This subscription plan is not available for your account type'
                ]);
            }

            DB::beginTransaction();

            // Create or get Stripe customer
            $stripeCustomer = $this->getOrCreateStripeCustomer($user);

            // Attach payment method to customer
            $this->stripe->paymentMethods->attach(
                $request->payment_method_id,
                ['customer' => $stripeCustomer->id]
            );

            // Set as default payment method
            $this->stripe->customers->update($stripeCustomer->id, [
                'invoice_settings' => [
                    'default_payment_method' => $request->payment_method_id
                ]
            ]);

            // Handle one-time payment
            if ($plan->billing_cycle === 'one_time') {
                $paymentIntent = $this->stripe->paymentIntents->create([
                    'amount' => $plan->price * 100, // Convert to cents
                    'currency' => 'usd',
                    'customer' => $stripeCustomer->id,
                    'payment_method' => $request->payment_method_id,
                    'confirm' => true,
                    'description' => "One-time payment for {$plan->name}",
                ]);

                // Create subscription record
                $subscription = Subscription::create([
                    'user_id' => $user->id,
                    'subscription_plan_id' => $plan->id,
                    'stripe_customer_id' => $stripeCustomer->id,
                    'status' => 'active',
                    'ends_at' => null, // Lifetime access
                ]);

                // Create transaction record
                Transaction::create([
                    'user_id' => $user->id,
                    'subscription_id' => $subscription->id,
                    'payment_method' => 'stripe',
                    'transaction_id' => $paymentIntent->id,
                    'amount' => $plan->price,
                    'currency' => 'usd',
                    'status' => 'completed',
                    'description' => "One-time payment for {$plan->name}",
                ]);

                DB::commit();

                return redirect()->route('mobile.subscription-success', ['token' => $token]);
            }

            // Handle recurring subscription
            $trialEndDate = $plan->trial_days > 0
                ? Carbon::now()->addDays($plan->trial_days)
                : null;

            $end_at = Carbon::now()->addMonth();

            if ($plan->billing_cycle === 'yearly') {
                $end_at = Carbon::now()->addYear();
            }

            // First create a product if it doesn't exist
            $productName = "Plan: {$plan->name}";
            $productId = "plan_{$plan->id}";

            try {
                // Try to retrieve the product first
                $product = $this->stripe->products->retrieve($productId);
            } catch (\Exception $e) {
                // Create the product if it doesn't exist
                $product = $this->stripe->products->create([
                    'id' => $productId,
                    'name' => $productName,
                    'description' => $plan->description ?? "Subscription plan for {$plan->user_type}",
                ]);
            }

            // Create a price for the product
            $price = $this->stripe->prices->create([
                'product' => $product->id,
                'unit_amount' => $plan->price * 100, // Convert to cents
                'currency' => 'usd',
                'recurring' => [
                    'interval' => $plan->billing_cycle === 'yearly' ? 'year' : 'month',
                ],
            ]);

            // Create Stripe subscription
            $stripeSubscription = $this->stripe->subscriptions->create([
                'customer' => $stripeCustomer->id,
                'items' => [
                    ['price' => $price->id],
                ],
                'payment_behavior' => 'default_incomplete',
                'payment_settings' => [
                    'payment_method_types' => ['card'],
                    'save_default_payment_method' => 'on_subscription',
                ],
                'expand' => ['latest_invoice'],
                'trial_end' => $trialEndDate ? $trialEndDate->timestamp : 'now',
                'default_payment_method' => $request->payment_method_id,
            ]);

            // Create subscription record
            $subscription = Subscription::create([
                'user_id' => $user->id,
                'subscription_plan_id' => $plan->id,
                'stripe_subscription_id' => $stripeSubscription->id,
                'stripe_customer_id' => $stripeCustomer->id,
                'stripe_price_id' => $stripeSubscription->items->data[0]->price->id,
                'status' => $trialEndDate ? 'trialing' : 'active',
                'trial_ends_at' => $trialEndDate,
                'next_billing_date' => $trialEndDate
                    ? $trialEndDate
                    : Carbon::createFromTimestamp($stripeSubscription->current_period_end ?? time())->addMonth(),
                'ends_at' => $end_at,
            ]);

            // If there's no trial, create a transaction record
            if (!$trialEndDate && isset($stripeSubscription->latest_invoice)) {
                Transaction::create([
                    'user_id' => $user->id,
                    'subscription_id' => $subscription->id,
                    'payment_method' => 'stripe',
                    'transaction_id' => $stripeSubscription->latest_invoice->id,
                    'amount' => $plan->price,
                    'currency' => 'usd',
                    'status' => 'completed',
                    'description' => "Initial payment for {$plan->name} subscription",
                ]);
            }

            DB::commit();

            return redirect()->route('mobile.subscription-success', ['token' => $token]);
        } catch (CardException $e) {
            DB::rollBack();
            Log::error('Stripe card error: ' . $e->getMessage());
            return view('mobile.subscription.error', [
                'message' => 'Payment failed: ' . $e->getMessage()
            ]);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Subscription error: ' . $e->getMessage());
            return view('mobile.subscription.error', [
                'message' => 'Failed to create subscription: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Subscription success page
     */
    public function subscriptionSuccess($token)
    {
        try {
            $user = $this->getUserFromToken($token);
            $subscription = $user->subscription;

            if (!$subscription) {
                return view('mobile.subscription.error', [
                    'message' => 'No active subscription found'
                ]);
            }

            return view('mobile.subscription.success', [
                'user' => $user,
                'subscription' => $subscription->load('plan')
            ]);
        } catch (Exception $e) {
            Log::error('Error showing subscription success: ' . $e->getMessage());
            return view('mobile.subscription.error', [
                'message' => 'Failed to load subscription details: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Cancel subscription
     */
    public function cancelSubscription($token)
    {
        try {
            $user = $this->getUserFromToken($token);
            $subscription = $user->subscription;

            if (!$subscription) {
                return view('mobile.subscription.error', [
                    'message' => 'No active subscription found'
                ]);
            }

            // For one-time payments, just mark as canceled
            if (!$subscription->stripe_subscription_id) {
                $subscription->update([
                    'status' => 'canceled',
                    'ends_at' => Carbon::now(),
                ]);

                return view('mobile.subscription.canceled', [
                    'message' => 'Subscription canceled successfully'
                ]);
            }

            // For recurring subscriptions, cancel in Stripe
            $stripeSubscription = $this->stripe->subscriptions->retrieve($subscription->stripe_subscription_id);

            // Cancel at period end
            $this->stripe->subscriptions->update($subscription->stripe_subscription_id, [
                'cancel_at_period_end' => true
            ]);

            // Update local subscription
            $subscription->update([
                'cancel_at_period_end' => true,
                'ends_at' => Carbon::createFromTimestamp($stripeSubscription->current_period_end ?? time()),
            ]);

            return view('mobile.subscription.canceled', [
                'message' => 'Subscription will be canceled at the end of the billing period',
                'subscription' => $subscription->load('plan')
            ]);
        } catch (Exception $e) {
            Log::error('Error canceling subscription: ' . $e->getMessage());
            return view('mobile.subscription.error', [
                'message' => 'Failed to cancel subscription: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get or create Stripe customer for user
     */
    protected function getOrCreateStripeCustomer(User $user)
    {
        // Check if user already has a Stripe customer ID
        $subscription = $user->subscriptions()->first();

        if ($subscription && $subscription->stripe_customer_id) {
            return $this->stripe->customers->retrieve($subscription->stripe_customer_id);
        }

        // Create new Stripe customer
        return $this->stripe->customers->create([
            'email' => $user->email,
            'name' => $user->name,
            'metadata' => [
                'user_id' => $user->id,
                'role' => $user->role
            ]
        ]);
    }
}
