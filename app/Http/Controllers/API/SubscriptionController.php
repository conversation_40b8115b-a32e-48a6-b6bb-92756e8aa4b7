<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use App\Models\Transaction;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Stripe\Exception\CardException;
use Stripe\Stripe;
use Stripe\StripeClient;

class SubscriptionController extends Controller
{
    protected $stripe;

    public function __construct()
    {
        // Initialize Stripe with API key from config
        Stripe::setApiKey(env('STRIPE_SECRET'));
        $this->stripe = new StripeClient(env('STRIPE_SECRET'));
    }

    /**
     * Get all subscription plans
     */
    public function getPlans($userType = null)
    {
        try {
            // For authenticated routes, use the authenticated user's role
            if (Auth::check() && !$userType) {
                // If user type is not provided, use the authenticated user's role
                $userType = Auth::user()->role;
            }

            // Validate user type
            if (!in_array($userType, ['musician', 'client'])) {
                return jsonResponse(false, [
                    'message' => 'Invalid user type. Must be either "musician" or "client".'
                ]);
            }

            $plans = SubscriptionPlan::where('user_type', $userType)
                ->where('is_active', true)
                ->get();

            return jsonResponse(true, [
                'plans' => $plans
            ]);
        } catch (Exception $e) {
            Log::error('Error fetching subscription plans: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => 'Failed to fetch subscription plans: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get user's active subscription
     */
    public function getSubscription()
    {
        try {
            $user = Auth::user();
            $subscription = $user->subscription;

            if (!$subscription) {
                return jsonResponse(true, [
                    'has_subscription' => false,
                    'message' => 'No active subscription found',
                    'subscription' => null
                ]);
            }

            $subscription->load('plan');

            return jsonResponse(true, [
                'has_subscription' => true,
                'subscription' => $subscription
            ]);
        } catch (Exception $e) {
            Log::error('Error fetching subscription: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => 'Failed to fetch subscription: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Subscribe to a plan
     */
    public function subscribe(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'plan_id' => 'required|exists:subscription_plans,id',
                'payment_method_id' => 'required|string',
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $user = Auth::user();
            $plan = SubscriptionPlan::findOrFail($request->plan_id);

            // Check if user already has an active subscription
            if ($user->hasActiveSubscription()) {
                return jsonResponse(false, [
                    'message' => 'You already have an active subscription'
                ]);
            }

            // Check if plan is appropriate for user role
            if ($plan->user_type !== $user->role) {
                return jsonResponse(false, [
                    'message' => 'This subscription plan is not available for your account type'
                ]);
            }

            DB::beginTransaction();

            // Create or get Stripe customer
            $stripeCustomer = $this->getOrCreateStripeCustomer($user);

            // Attach payment method to customer
            $this->stripe->paymentMethods->attach(
                $request->payment_method_id,
                ['customer' => $stripeCustomer->id]
            );

            // Set as default payment method
            $this->stripe->customers->update($stripeCustomer->id, [
                'invoice_settings' => [
                    'default_payment_method' => $request->payment_method_id
                ]
            ]);

            // Handle one-time payment
            if ($plan->billing_cycle === 'one_time') {
                $paymentIntent = $this->stripe->paymentIntents->create([
                    'amount' => $plan->price * 100, // Convert to cents
                    'currency' => 'usd',
                    'customer' => $stripeCustomer->id,
                    'payment_method' => $request->payment_method_id,
                    'confirm' => true,
                    'description' => "One-time payment for {$plan->name}",
                ]);

                // Create subscription record
                $subscription = Subscription::create([
                    'user_id' => $user->id,
                    'subscription_plan_id' => $plan->id,
                    'stripe_customer_id' => $stripeCustomer->id,
                    'status' => 'active',
                    'ends_at' => null, // Lifetime access
                ]);

                // Create transaction record
                Transaction::create([
                    'user_id' => $user->id,
                    'subscription_id' => $subscription->id,
                    'payment_method' => 'stripe',
                    'transaction_id' => $paymentIntent->id,
                    'amount' => $plan->price,
                    'currency' => 'usd',
                    'status' => 'completed',
                    'description' => "One-time payment for {$plan->name}",
                ]);

                DB::commit();

                return jsonResponse(true, [
                    'message' => 'Payment successful',
                    'subscription' => $subscription->load('plan')
                ]);
            }

            // Handle recurring subscription
            $trialEndDate = $plan->trial_days > 0
                ? Carbon::now()->addDays($plan->trial_days)
                : null;

            // First create a product if it doesn't exist
            $productName = "Plan: {$plan->name}";
            $productId = "plan_{$plan->id}";

            try {
                // Try to retrieve the product first
                $product = $this->stripe->products->retrieve($productId);
            } catch (\Exception $e) {
                // Create the product if it doesn't exist
                $product = $this->stripe->products->create([
                    'id' => $productId,
                    'name' => $productName,
                    'description' => $plan->description ?? "Subscription plan for {$plan->user_type}",
                ]);
            }

            // Create a price for the product
            $price = $this->stripe->prices->create([
                'product' => $product->id,
                'unit_amount' => $plan->price * 100, // Convert to cents
                'currency' => 'usd',
                'recurring' => [
                    'interval' => $plan->billing_cycle === 'yearly' ? 'year' : 'month',
                ],
            ]);

            // Create Stripe subscription
            $stripeSubscription = $this->stripe->subscriptions->create([
                'customer' => $stripeCustomer->id,
                'items' => [
                    ['price' => $price->id],
                ],
                'payment_behavior' => 'default_incomplete',
                'payment_settings' => [
                    'payment_method_types' => ['card'],
                    'save_default_payment_method' => 'on_subscription',
                ],
                'expand' => ['latest_invoice'],
                'trial_end' => $trialEndDate ? $trialEndDate->timestamp : 'now',
                'default_payment_method' => $request->payment_method_id,
            ]);

            // Create subscription record
            $subscription = Subscription::create([
                'user_id' => $user->id,
                'subscription_plan_id' => $plan->id,
                'stripe_subscription_id' => $stripeSubscription->id,
                'stripe_customer_id' => $stripeCustomer->id,
                'stripe_price_id' => $stripeSubscription->items->data[0]->price->id,
                'status' => $trialEndDate ? 'trialing' : 'active',
                'trial_ends_at' => $trialEndDate,
                'next_billing_date' => $trialEndDate
                    ? $trialEndDate
                    : Carbon::createFromTimestamp($stripeSubscription->current_period_end ?? time())->addMonth(),
            ]);

            // If there's no trial, create a transaction record
            if (!$trialEndDate && isset($stripeSubscription->latest_invoice->payment_intent)) {
                Transaction::create([
                    'user_id' => $user->id,
                    'subscription_id' => $subscription->id,
                    'payment_method' => 'stripe',
                    'transaction_id' => $stripeSubscription->latest_invoice->payment_intent->id,
                    'amount' => $plan->price,
                    'currency' => 'usd',
                    'status' => 'completed',
                    'description' => "Initial payment for {$plan->name} subscription",
                ]);
            }

            DB::commit();

            // Get client secret if available
            $clientSecret = null;
            if (isset($stripeSubscription->latest_invoice->payment_intent)) {
                $clientSecret = $stripeSubscription->latest_invoice->payment_intent->client_secret;
            }

            return jsonResponse(true, [
                'message' => $trialEndDate
                    ? 'Subscription started with free trial'
                    : 'Subscription created successfully',
                'subscription' => $subscription->load('plan'),
                'client_secret' => $clientSecret,
            ]);
        } catch (CardException $e) {
            DB::rollBack();
            Log::error('Stripe card error: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => 'Payment failed: ' . $e->getMessage()
            ]);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Subscription error: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => 'Failed to create subscription: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Cancel subscription
     */
    public function cancelSubscription()
    {
        try {
            $user = Auth::user();
            $subscription = $user->subscription;

            if (!$subscription) {
                return jsonResponse(false, [
                    'message' => 'No active subscription found'
                ]);
            }

            // For one-time payments, just mark as canceled
            if (!$subscription->stripe_subscription_id) {
                $subscription->update([
                    'status' => 'canceled',
                    'ends_at' => Carbon::now(),
                ]);

                return jsonResponse(true, [
                    'message' => 'Subscription canceled successfully'
                ]);
            }

            // For recurring subscriptions, cancel in Stripe
            $stripeSubscription = $this->stripe->subscriptions->retrieve($subscription->stripe_subscription_id);

            // Cancel at period end
            $this->stripe->subscriptions->update($subscription->stripe_subscription_id, [
                'cancel_at_period_end' => true
            ]);

            // Update local subscription
            $subscription->update([
                'cancel_at_period_end' => true,
                'ends_at' => Carbon::createFromTimestamp($stripeSubscription->current_period_end ?? time()),
            ]);

            return jsonResponse(true, [
                'message' => 'Subscription will be canceled at the end of the billing period',
                'subscription' => $subscription->load('plan')
            ]);
        } catch (Exception $e) {
            Log::error('Error canceling subscription: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => 'Failed to cancel subscription: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Resume a canceled subscription
     */
    public function resumeSubscription()
    {
        try {
            $user = Auth::user();
            $subscription = $user->subscription;

            if (!$subscription || !$subscription->cancel_at_period_end) {
                return jsonResponse(false, [
                    'message' => 'No canceled subscription found to resume'
                ]);
            }

            // Resume subscription in Stripe
            $this->stripe->subscriptions->update($subscription->stripe_subscription_id, [
                'cancel_at_period_end' => false
            ]);

            // Update local subscription
            $subscription->update([
                'cancel_at_period_end' => false,
                'ends_at' => null,
            ]);

            return jsonResponse(true, [
                'message' => 'Subscription resumed successfully',
                'subscription' => $subscription->load('plan')
            ]);
        } catch (Exception $e) {
            Log::error('Error resuming subscription: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => 'Failed to resume subscription: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get transaction history
     */
    public function getTransactions()
    {
        try {
            $user = Auth::user();
            $transactions = $user->transactions()
                ->with('subscription.plan')
                ->latest()
                ->paginate(10);

            return jsonResponse(true, [
                'transactions' => $transactions->items(),
                'pagination' => [
                    'total' => $transactions->total(),
                    'per_page' => $transactions->perPage(),
                    'current_page' => $transactions->currentPage(),
                    'last_page' => $transactions->lastPage(),
                ]
            ]);
        } catch (Exception $e) {
            Log::error('Error fetching transactions: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => 'Failed to fetch transactions: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get subscription web view URLs
     */
    public function getSubscriptionUrls(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'plan_id' => 'required|exists:subscription_plans,id',
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $user = Auth::user();
            $planId = $request->plan_id;

            // Generate URLs for subscription actions
            $subscribeUrl = route('mobile.subscribe', [
                'token' => $user->createToken('subscription-token')->plainTextToken,
                'planId' => $planId
            ]);

            $cancelUrl = route('mobile.cancel-subscription', [
                'token' => $user->createToken('subscription-token')->plainTextToken
            ]);

            return jsonResponse(true, [
                'subscribe_url' => $subscribeUrl,
                'cancel_url' => $cancelUrl
            ]);
        } catch (Exception $e) {
            Log::error('Error generating subscription URLs: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => 'Failed to generate subscription URLs: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get or create Stripe customer for user
     */
    protected function getOrCreateStripeCustomer(User $user)
    {
        // Check if user already has a Stripe customer ID
        $subscription = $user->subscriptions()->first();

        if ($subscription && $subscription->stripe_customer_id) {
            return $this->stripe->customers->retrieve($subscription->stripe_customer_id);
        }

        // Create new Stripe customer
        return $this->stripe->customers->create([
            'email' => $user->email,
            'name' => $user->name,
            'metadata' => [
                'user_id' => $user->id,
                'role' => $user->role
            ]
        ]);
    }
}
