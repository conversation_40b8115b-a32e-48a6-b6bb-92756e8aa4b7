<?php

namespace App\Http\Controllers\API;

use App\Events\MessageSent;
use App\Events\UnreadCountUpdated;
use App\Http\Controllers\Controller;
use App\Models\Chat;
use App\Models\FavoriteMusician;
use App\Models\Message;
use App\Models\User;
use App\Services\FCMService;
use App\Traits\FirebaseDatabaseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ChatController extends Controller
{
    use FirebaseDatabaseTrait;

    public function sendMessage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'receiver_id' => 'required|exists:users,id',
            'message' => 'required|string',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        $sender = auth()->user();


        $authUserId = $sender->id;
        $otherUserId = $request->receiver_id;

        if ($authUserId == $otherUserId) {
            return jsonResponse(false, ['message' => 'You cannot send a message to yourself!']);
        }

        // Optimized Chat Lookup
        $chat = Chat::firstOrCreate(
            ['client_id' => min($authUserId, $otherUserId), 'musician_id' => max($authUserId, $otherUserId)]
        );

        // Check if chat was just created
        $isNewChat = $chat->wasRecentlyCreated;

        // Save Message
        $message = Message::create([
            'chat_id' => $chat->id,
            'sender_id' => $authUserId,
            'message' => $request->message,
            'is_read' => 0,
        ]);

        broadcast(new MessageSent($message, $otherUserId, $isNewChat))->toOthers();

        // Fetch receiver details
        $receiver = User::where('id', $otherUserId)->first('fcm_token', 'id');

        // Broadcast unread count update
        // broadcast(new UnreadCountUpdated($receiver->id))->toOthers();
        $this->updateUnreadCount('chat', $otherUserId, $chat->id);



        if ($receiver && $receiver->fcm_token) {
            $title = 'New Message from ' . $sender->name;
            $body = null;
            if ($request->message) {
                $body = $request->message;
            }

            FCMService::sendNotification(
                $receiver->fcm_token,
                $title,
                $body
            );
        }

        return jsonResponse(true, [
            'message' => 'Message sent successfully',
            'data' => $message
        ]);
    }


    public function getMessages($userId)
    {
        if (!$userId) {
            return jsonResponse(false, ['message' => 'User ID is required']);
        }

        $authUser = auth()->user();

        if ($userId == $authUser->id) {
            return jsonResponse(false, ['message' => 'You cannot send a message to yourself!']);
        }

        $authUserId = $authUser->id;
        $otherUserId = $userId;

        // Find chat between two users
        $chat = Chat::firstOrCreate(
            ['client_id' => min($authUserId, $otherUserId), 'musician_id' => max($authUserId, $otherUserId)]
        );

        // load other user
        $otherUser = User::where('id', $otherUserId)->first(['id', 'name', 'image']);

        $chat->sender = $otherUser;

        // Mark messages as read where the authenticated user is the receiver
        $updatedCount = Message::where('chat_id', $chat->id)
            ->where('sender_id', '!=', $authUserId)
            ->where('is_read', false)
            ->update(['is_read' => true]);

        // Only broadcast if at least one message was marked as read
        if ($updatedCount > 0) {
            // broadcast(new UnreadCountUpdated($authUserId))->toOthers();
            $this->updateUnreadCount('chat', $authUserId, $chat->id);
        }

        // Fetch messages in ascending order (oldest first)
        $messages = Message::where('chat_id', $chat->id)
            ->latest()
            ->paginate(10);


        return jsonResponse(true, [
            'messages' => $messages->items(),
            'chat' => $chat,
            'pagination' => [
                'total' => $messages->total(),
                'per_page' => $messages->perPage(),
                'current_page' => $messages->currentPage(),
                'last_page' => $messages->lastPage(),
            ],
        ]);
    }


    public function getChats()
    {
        $authUser = auth()->user();
        $authUserId = $authUser->id;

        $chats = Chat::where(function ($query) use ($authUserId) {
            $query->where('client_id', $authUserId)
                ->orWhere('musician_id', $authUserId);
        })
            ->whereHas('messages')
            ->with([
                'client:id,name,image',
                'musician:id,name,image,availability',
                'musician.musicianProfile:rate_per_hour,rate_per_event,description,roles,offered_services,instruments,music_types,spoken_languages,payment_methods,tags,location,average_rating,ratings_count',
                'messages' => function ($query) {
                    $query->latest()->limit(1); // Fetch only the last message
                }
            ])
            ->withCount([
                'messages as unread_count' => function ($query) use ($authUserId) {
                    $query->where('is_read', false)
                        ->where('sender_id', '!=', $authUserId); // Only count unread incoming messages
                }
            ])
            ->withMax('messages', 'created_at') // Get latest message timestamp
            ->orderByDesc('messages_max_created_at') // Sort by latest message timestamp
            ->paginate(10); // Pagination limit of 10

        // Transform response
        $chats->getCollection()->transform(function ($chat) use ($authUserId) {
            $otherUser = $chat->client_id == $authUserId ? $chat->musician : $chat->client;
            $lastMessage = $chat->messages->first();

            return [
                'chat_id' => $chat->id,
                'sender' => [
                    'id' => $otherUser->id,
                    'name' => $otherUser->name,
                    'image' => $otherUser->image_url,
                    'rate_per_hour' => $otherUser->musicianProfile?->rate_per_hour,
                    'rate_per_event' => $otherUser->musicianProfile?->rate_per_event,
                    'description' => $otherUser->musicianProfile?->description,
                    'roles' => $otherUser->musicianProfile?->roles,
                    'offered_services' => $otherUser->musicianProfile?->offered_services,
                    'instruments' => $otherUser->musicianProfile?->instruments,
                    'music_types' => $otherUser->musicianProfile?->music_types,
                    'spoken_languages' => $otherUser->musicianProfile?->spoken_languages,
                    'payment_methods' => $otherUser->musicianProfile?->payment_methods,
                    'tags' => $otherUser->musicianProfile?->tags,
                    'location' => $otherUser->musicianProfile?->location,
                    'availability' => $otherUser->availability,
                    'average_rating' => $otherUser->musicianProfile?->average_rating,
                    'ratings_count' => $otherUser->musicianProfile?->ratings_count,
                    'is_favorite' => FavoriteMusician::where('user_id', $authUserId)
                        ->where('musician_id', $otherUser->id)
                        ->exists()
                ],
                'last_message' => $lastMessage ? [
                    'id' => $lastMessage->id,
                    'message' => $lastMessage->message,
                    'sender_id' => $lastMessage->sender_id,
                    'is_read' => $lastMessage->is_read,
                    'created_at' => $lastMessage->formatted_created_at,
                ] : null,
                'unread_count' => $chat->unread_count,
            ];
        });


        return jsonResponse(true, [
            'chats' => $chats->items(),
            'pagination' => [
                'total' => $chats->total(),
                'per_page' => $chats->perPage(),
                'current_page' => $chats->currentPage(),
                'last_page' => $chats->lastPage(),
            ],
        ]);
    }


    public function getTotalUnreadMessages()
    {
        $authUserId = auth()->id();

        $totalUnread = Message::whereHas('chat', function ($query) use ($authUserId) {
            $query->where('client_id', $authUserId)
                ->orWhere('musician_id', $authUserId);
        })
            ->where('is_read', false)
            ->where('sender_id', '!=', $authUserId) // Only count unread incoming messages
            ->count();

        return jsonResponse(true, ['total_unread' => $totalUnread]);
    }
}
